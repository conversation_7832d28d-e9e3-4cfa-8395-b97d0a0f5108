<?php
/**
 * View file for Pre-Screening Detail Report
 *
 * @var array $application Application details with pre-screening information
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/pre-screening/' . $application['exercise_id']) ?>">Pre-Screening Report</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Pre-Screening Detail - <?= esc($application['first_name'] . ' ' . $application['last_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Pre-Screening Detail Report</h2>
                    <p class="text-muted mb-0"><?= esc($application['first_name'] . ' ' . $application['last_name']) ?> - <?= esc($application['application_number']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/pre-screening/' . $application['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Pre-Screening Report
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Applicant Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Applicant Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Full Name:</dt>
                                <dd class="col-sm-8"><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></dd>
                                <dt class="col-sm-4">Application No:</dt>
                                <dd class="col-sm-8"><?= esc($application['application_number']) ?></dd>
                                <dt class="col-sm-4">Gender:</dt>
                                <dd class="col-sm-8"><?= esc($application['gender']) ?></dd>
                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8"><?= esc($application['email_address']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Exercise:</dt>
                                <dd class="col-sm-8"><?= esc($application['exercise_name']) ?></dd>
                                <dt class="col-sm-4">Advertisement No:</dt>
                                <dd class="col-sm-8"><?= esc($application['advertisement_no'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Advertisement Date:</dt>
                                <dd class="col-sm-8"><?= $application['advertisement_date'] ? date('M d, Y', strtotime($application['advertisement_date'])) : 'N/A' ?></dd>
                                <dt class="col-sm-4">Contact:</dt>
                                <dd class="col-sm-8">
                                    <?php
                                    $contact = json_decode($application['contact_details'], true);
                                    echo esc($contact['phone'] ?? 'N/A');
                                    ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Exercise Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Exercise Name:</dt>
                                <dd class="col-sm-8"><?= esc($application['exercise_name']) ?></dd>
                                <dt class="col-sm-4">Gazzetted No:</dt>
                                <dd class="col-sm-8"><?= esc($application['gazzetted_no'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Gazzetted Date:</dt>
                                <dd class="col-sm-8"><?= $application['gazzetted_date'] ? date('M d, Y', strtotime($application['gazzetted_date'])) : 'N/A' ?></dd>
                                <dt class="col-sm-4">Exercise Type:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-<?= $application['is_internal'] ? 'info' : 'primary' ?>">
                                        <?= $application['is_internal'] ? 'Internal' : 'External' ?>
                                    </span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Advertisement Mode:</dt>
                                <dd class="col-sm-8"><?= esc($application['mode_of_advertisement'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Publish From:</dt>
                                <dd class="col-sm-8"><?= $application['publish_date_from'] ? date('M d, Y', strtotime($application['publish_date_from'])) : 'N/A' ?></dd>
                                <dt class="col-sm-4">Publish To:</dt>
                                <dd class="col-sm-8"><?= $application['publish_date_to'] ? date('M d, Y', strtotime($application['publish_date_to'])) : 'N/A' ?></dd>
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-<?= $application['exercise_status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst($application['exercise_status'] ?? 'N/A') ?>
                                    </span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <?php if (!empty($application['description'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <dl class="row">
                                <dt class="col-sm-2">Description:</dt>
                                <dd class="col-sm-10"><?= esc($application['description']) ?></dd>
                            </dl>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Pre-Screening Results -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Pre-Screening Results</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-<?= $application['pre_screened_status'] === 'passed' ? 'success' : 'danger' ?>">
                                        <i class="fas fa-<?= $application['pre_screened_status'] === 'passed' ? 'check' : 'times' ?> me-1"></i>
                                        <?= ucfirst($application['pre_screened_status']) ?>
                                    </span>
                                </dd>
                                <dt class="col-sm-4">Screened By:</dt>
                                <dd class="col-sm-8"><?= esc($application['screened_by_username'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Date Screened:</dt>
                                <dd class="col-sm-8"><?= date('M d, Y H:i', strtotime($application['pre_screened_at'])) ?></dd>
                            </dl>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <dl class="row">
                                <dt class="col-sm-2">Remarks:</dt>
                                <dd class="col-sm-10"><?= esc($application['pre_screened_remarks']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Criteria Evaluation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Criteria Evaluation</h6>
                </div>
                <div class="card-body">
                    <?php 
                    $criteriaResults = json_decode($application['pre_screened_criteria_results'], true);
                    ?>
                    <?php if ($criteriaResults && is_array($criteriaResults) && isset($criteriaResults['criteria_evaluations'])): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Criteria</th>
                                        <th>Result</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($criteriaResults['criteria_evaluations'] as $index => $evaluation): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td><?= esc($evaluation['criteria_name']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $evaluation['evaluation_result'] === 'MEETS' ? 'success' : 'danger' ?>">
                                                    <?= esc($evaluation['evaluation_result']) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M d, Y H:i', strtotime($evaluation['timestamp'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Summary -->
                        <?php if (isset($criteriaResults['overall_summary'])): ?>
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <h6>Overall Summary</h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h4 class="text-primary"><?= $criteriaResults['overall_summary']['total_criteria'] ?></h4>
                                                <p class="mb-0">Total Criteria</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h4 class="text-success"><?= $criteriaResults['overall_summary']['passed'] ?></h4>
                                                <p class="mb-0">Passed</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h4 class="text-danger"><?= $criteriaResults['overall_summary']['failed'] ?></h4>
                                                <p class="mb-0">Failed</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h4 class="text-warning"><?= $criteriaResults['overall_summary']['pending'] ?></h4>
                                                <p class="mb-0">Pending</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No criteria evaluation data available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Analysis (if available) -->
    <?php if (!empty($application['pre_screened_ai_analysis'])): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">AI Analysis</h6>
                    </div>
                    <div class="card-body">
                        <?php 
                        $aiAnalysis = json_decode($application['pre_screened_ai_analysis'], true);
                        ?>
                        <?php if ($aiAnalysis && is_array($aiAnalysis)): ?>
                            <pre class="bg-light p-3 rounded"><?= esc(json_encode($aiAnalysis, JSON_PRETTY_PRINT)) ?></pre>
                        <?php else: ?>
                            <p class="text-muted">AI analysis data is not in a readable format</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>
