<?php
/**
 * View file for Pre-Screening Report
 *
 * @var array $exercise Exercise details
 * @var array $pre_screening_results List of pre-screening results
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Exercise Reports</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Pre-Screening Report - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Pre-Screening Report</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Exercise Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Exercise Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Exercise Name:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['exercise_name']) ?></dd>
                                <dt class="col-sm-4">Advertisement No:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['advertisement_no'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Advertisement Date:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['advertisement_date'] ?? 'N/A') ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Gazzetted No:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['gazzetted_no'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Gazzetted Date:</dt>
                                <dd class="col-sm-8"><?= esc($exercise['gazzetted_date'] ?? 'N/A') ?></dd>
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-<?= $exercise['status'] === 'published' ? 'success' : ($exercise['status'] === 'draft' ? 'warning' : 'info') ?>">
                                        <?= ucfirst(esc($exercise['status'])) ?>
                                    </span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pre-Screening Results -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Pre-Screening Results (<?= count($pre_screening_results) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($pre_screening_results)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No pre-screening results found</h5>
                            <p class="text-muted">No applications have been pre-screened for this exercise.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="preScreeningTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Application No.</th>
                                        <th>Full Name</th>
                                        <th>Gender</th>
                                        <th>Contact Details</th>
                                        <th>Status</th>
                                        <th>Criteria Results</th>
                                        <th>Date Screened</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pre_screening_results as $index => $result): ?>
                                        <?php
                                        $contact = json_decode($result['contact_details'], true);
                                        $statusClass = $result['pre_screened_status'] === 'passed' ? 'success' : 'danger';
                                        $criteriaResults = json_decode($result['pre_screened_criteria_results'], true);
                                        ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong><?= esc($result['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($result['first_name'] . ' ' . $result['last_name']) ?></strong>
                                                </div>
                                            </td>
                                            <td><?= esc($result['gender']) ?></td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Phone:</strong> <?= esc($contact['phone'] ?? $result['email_address'] ?? 'N/A') ?></div>
                                                    <div><strong>Email:</strong> <?= esc($contact['email'] ?? $result['email_address'] ?? 'N/A') ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $statusClass ?>">
                                                    <i class="fas fa-<?= $result['pre_screened_status'] === 'passed' ? 'check' : 'times' ?> me-1"></i>
                                                    <?= ucfirst($result['pre_screened_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="small" style="max-width: 200px;">
                                                    <?php if ($criteriaResults && is_array($criteriaResults) && isset($criteriaResults['criteria_evaluations'])): ?>
                                                        <?php foreach ($criteriaResults['criteria_evaluations'] as $evaluation): ?>
                                                            <div class="mb-1">
                                                                <strong><?= esc($evaluation['criteria_name']) ?>:</strong>
                                                                <span class="badge bg-<?= $evaluation['evaluation_result'] === 'MEETS' ? 'success' : 'danger' ?> ms-1">
                                                                    <?= esc($evaluation['evaluation_result']) ?>
                                                                </span>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">No criteria results</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?= date('M d, Y', strtotime($result['pre_screened_at'])) ?>
                                                    <br>
                                                    <?= date('H:i', strtotime($result['pre_screened_at'])) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('reports/pre-screening-detail/' . $result['id']) ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Pre-Screening Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($pre_screening_results) ?></h4>
                                <p class="mb-0">Total Screened</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($pre_screening_results, function($result) { return $result['pre_screened_status'] === 'passed'; })) ?>
                                </h4>
                                <p class="mb-0">Passed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger">
                                    <?= count(array_filter($pre_screening_results, function($result) { return $result['pre_screened_status'] === 'failed'; })) ?>
                                </h4>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php
                                $passedCount = count(array_filter($pre_screening_results, function($result) { return $result['pre_screened_status'] === 'passed'; }));
                                $passRate = count($pre_screening_results) > 0 ? round(($passedCount / count($pre_screening_results)) * 100, 1) : 0;
                                ?>
                                <h4 class="text-info"><?= $passRate ?>%</h4>
                                <p class="mb-0">Pass Rate</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Get exercise ID from the current page
    const exerciseId = <?= $exercise['id'] ?>;

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Prepare form data with CSRF token
    const formData = new FormData();
    formData.append('exercise_id', exerciseId);
    formData.append('<?= csrf_token() ?>', csrfToken);

    // Make AJAX request to export PDF
    fetch('<?= base_url('reports/pre-screening/export') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        button.innerHTML = originalText;
        button.disabled = false;

        if (data.success) {
            // Show success message first
            alert('Pre-screening report exported successfully as PDF!');

            // Open the file in a new window/tab for download
            window.open(data.file_url, '_blank');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        button.innerHTML = originalText;
        button.disabled = false;
        console.error('Export error:', error);
        alert('Failed to export report. Please try again.');
    });
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#preScreeningTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[7, 'desc'], [6, 'desc']],
            columnDefs: [
                { orderable: false, targets: [4, 5, 8] }
            ]
        });
    }
});
</script>
<?= $this->endSection() ?>
